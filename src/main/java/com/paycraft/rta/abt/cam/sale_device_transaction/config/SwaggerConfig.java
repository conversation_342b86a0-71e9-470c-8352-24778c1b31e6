package com.paycraft.rta.abt.cam.sale_device_transaction.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;


@Configuration
public class SwaggerConfig {

    public static final String HEADER = "header";

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info().title("Sale Device Transaction Service - PayCraft")
                        .version("1.0")
                        .description("This service is responsible for device sale transactions"))
                .components(new Components()
                        .addSecuritySchemes("basicAuth",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("basic")))
                .addSecurityItem(new SecurityRequirement().addList("basicAuth"));
    }

//    @Bean
//    public OperationCustomizer addGlobalHeaders() {
//        return (operation, handlerMethod) -> {
//            String endpointPath = handlerMethod.getMethod().getName();
//            var hmacNotRequiredEndpoints = new HashSet<>(Set.of("preRegister", "preRegisterBulk"));
//            if (!hmacNotRequiredEndpoints.contains(endpointPath)) {
//                operation.addParametersItem(new Parameter()
//                        .in(HEADER)
//                        .schema(new StringSchema())
//                        .name("X-HMAC-Signature")
//                        .description("HMAC Signature used for authentication. The signature is generated using the following formula:\n\n"
//                                + "HMAC Message = requestId + \"||\" + timestamp + \"||\" + requestMethod + \"||\" + requestPath + \"||\" + payload\n\n"
//                                + "Where:\n"
//                                + "- `requestId`: A unique identifier for the request sent in X-Client-Request-Identifier header.\n"
//                                + "- `timestamp`: The current UTC timestamp in ISO 8601 sent in X-Client-Request-DateTime header.\n"
//                                + "- `requestMethod`: The HTTP method (e.g., GET, POST, PUT, DELETE).\n"
//                                + "- `requestPath`: The API endpoint path.\n"
//                                + "- `payload`: The request body (empty string if no body).\n"
//                                + "- `shared_secret`: A secret key shared between client and server.\n\n"
//                                + "### Example:\n"
//                                + "#### Input Values:\n"
//                                + "- `requestId` = `95fad666-68b2-4ef6-a204-90c846c73685`\n"
//                                + "- `timestamp` = `2025-02-03T06:36:28.704Z`\n"
//                                + "- `requestMethod` = `POST`\n"
//                                + "- `requestPath` = `/dm/v1/devices/2/deviceDetails`\n"
//                                + "- `payload` = `{\n" +
//                                "    \"encDeviceRandomNumber\": \"testEncDeviceRandomNumber\",\n" +
//                                "    \"encDeviceSerialNumber\": \"testEncDeviceSerialNumber\"\n" +
//                                "}`\n"
//                                + "- `shared_secret` = `QvYOMTbodi2g/KTfQ46Vi7rfdtxRNQJIqiUKb3BioC0=`\n\n"
//                                + "#### Concatenated String:\n"
//                                + "```\n"
//                                + "95fad666-68b2-4ef6-a204-90c846c73685||2025-02-03T06:36:28.704Z||POST||/dm/v1/devices/2/deviceDetails||{\n" +
//                                "    \"encDeviceRandomNumber\": \"testEncDeviceRandomNumber\",\n" +
//                                "    \"encDeviceSerialNumber\": \"testEncDeviceSerialNumber\"\n" +
//                                "}\n"
//                                + "```\n\n"
//                                + "#### HMAC_SHA256 Signature:\n"
//                                + "```\n"
//                                + "base64(HmacSHA256(HMAC Message, shared_secret)) = iSutVFaI0K5Xh6wn8JkDIVQHuO3g8pAbtKjMDomEmPk=\n"
//                                + "```\n\n"
//                                + "The resulting Base64-encoded HMAC signature is sent in the `X-HMAC-Signature` header.")
//                        .required(true));
//
//            }
//            operation.addParametersItem(new Parameter().in(HEADER).schema(new StringSchema()).name("X-Client-Request-Identifier").description("Unique ID for the request").required(true));
//            operation.addParametersItem(new Parameter().in(HEADER).schema(new StringSchema()).name("X-Client-Request-DateTime").description("Request UTC timestamp").required(true));
//            operation.addParametersItem(new Parameter().in(HEADER).schema(new StringSchema()).name("X-Client-Device-Id").description("Device Id of the Requesting Device").required(true));
//            return operation;
//        };
//    }
}
