package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementRequestDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.GeneralTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.GeneralTransactionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service

public class GeneralTransactionServiceImpl implements GeneralTransactionService {
    private final Logger log = LogManager.getLogger(GeneralTransactionServiceImpl.class);

    @Autowired
    private GeneralTransactionClient generalTransactionClient;

    @Override
    public AppResponseDTO<AutoReloadRenewalResponseDTO> processAutoReload(AutoReloadRequestDTO request) {
        return generalTransactionClient.processAutoReload(request);
    }

    @Override
    public AppResponseDTO<TransactionDetailsDTO> updateAutoReloadSetting(UpdateAutoReloadRequestDTO request) {
        return generalTransactionClient.updateAutoReloadSetting(request);
    }

    @Override
    public AppResponseDTO<AutoReloadRenewalResponseDTO> processAutoRenewal(AutoRenewalRequestDTO request) {
        return generalTransactionClient.processAutoRenewal(request);
    }

    @Override
    public AppResponseDTO<TransactionDetailsDTO> updateAutoRenewalSetting(UpdateAutoRenewalRequestDTO request) {
        return generalTransactionClient.updateAutoRenewalSetting(request);
    }


    @Override
    public AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO> getAutoReloadSettingDetails(EnquiryDTO request) {
        return generalTransactionClient.getAutoReloadSettingDetails(request);
    }

    @Override
    public AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO> getAutoRenewalSettingDetails(EnquiryDTO request) {
        return generalTransactionClient.getAutoRenewalSettingDetails(request);
    }

    @Override
    public AppResponseDTO<TransactionStatementEnquiryResponseDTO> getTransactionFeeDetails(TransactionStatementEnquiryRequestDTO request) {
        return generalTransactionClient.getTransactionFeeDetails(request);
    }

    @Override
    public AppResponseDTO<TransactionHistoryResponseDTO> getTransactionHistory(TransactionHistoryRequestDTO request) {
        return generalTransactionClient.getTransactionHistory(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> getTransactionStatement(TransactionStatementRequestDTO request) {
        return generalTransactionClient.getTransactionStatement(request);
    }
}
