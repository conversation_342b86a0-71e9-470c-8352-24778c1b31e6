package com.paycraft.rta.abt.cam.sale_device_transaction.feign;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardSalesDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardConfigurationDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementRequestDTO;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(name = "general-transaction", url = "http://localhost:9002")
public interface GeneralTransactionClient {

    @GetMapping(value = "/general-transaction/v1/card-configuration", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardSalesDetailsDTO> getCardConfigurationDetails();

    @PostMapping(value = "/general-transaction/v1/setting/auto-reload", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<AutoReloadRenewalResponseDTO> processAutoReload(AutoReloadRequestDTO request);

    @PostMapping(value = "/general-transaction/v1/setting/auto-reload/update", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TransactionDetailsDTO> updateAutoReloadSetting(UpdateAutoReloadRequestDTO request);

    @PostMapping(value = "/general-transaction/v1/setting/auto-renewal", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<AutoReloadRenewalResponseDTO> processAutoRenewal(AutoRenewalRequestDTO request);

    @PostMapping(value = "/general-transaction/v1/setting/auto-renewal/update", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TransactionDetailsDTO> updateAutoRenewalSetting(UpdateAutoRenewalRequestDTO request);

    @PostMapping(value = "/general-transaction/v1/setting/auto-reload/details", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO> getAutoReloadSettingDetails(EnquiryDTO request);

    @PostMapping(value = "/general-transaction/v1/setting/auto-renewal/details", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO> getAutoRenewalSettingDetails(EnquiryDTO request);

    @PostMapping(value = "/general-transaction/v1/transaction/fee", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TransactionStatementEnquiryResponseDTO> getTransactionFeeDetails(TransactionStatementEnquiryRequestDTO request);

    @PostMapping(value = "/general-transaction/v1/transaction/history", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TransactionHistoryResponseDTO> getTransactionHistory(TransactionHistoryRequestDTO request);

    @PostMapping(value = "/general-transaction/v1/transaction/statement", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> getTransactionStatement(TransactionStatementRequestDTO request);
}
