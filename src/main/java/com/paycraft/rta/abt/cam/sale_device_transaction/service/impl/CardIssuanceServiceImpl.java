package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.TransactionTypeEnum;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.CardTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardIssuanceService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
public class CardIssuanceServiceImpl implements CardIssuanceService {
    private final Logger log = LogManager.getLogger(CardIssuanceServiceImpl.class);

    @Autowired
    private CardTransactionClient cardTransactionClient;

    @Autowired
    private DecryptionService decryptionService;

    @Override
    @Transactional
    public AppResponseDTO<CardIssuanceResponseDTO> issueCard(CardIssuanceRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardSaleDetails());
        return cardTransactionClient.issueCard(request);
    }

    @Override
    public AppResponseDTO<CardIssuanceResponseDTO> cardIssuanceStatus(TransactionStatusRequestDTO request) {
        return cardTransactionClient.issueCardStatus(request);
    }
}