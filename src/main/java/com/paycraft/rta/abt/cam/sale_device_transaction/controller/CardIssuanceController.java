package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardIssuanceService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.CARD_ISSUANCE;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.ISSUANCE_STATUS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.UPDATE_TOKEN;


@Validated
@RestController
public class CardIssuanceController {
    private final Logger log = LogManager.getLogger(CardIssuanceController.class);

    @Autowired
    private CardIssuanceService cardIssuanceService;

    @Autowired
    private DecryptionService decryptionService;

    @PostMapping(CARD_ISSUANCE)
    @Operation(
            summary = "Issuance of card",
            description = "Issue an anonymous card for a user. This endpoint processes the request and returns a result indicating success or failure of the issuance."
    )
    public ResponseEntity<AppResponseDTO<CardIssuanceResponseDTO>> cardIssuance(@Valid @RequestBody CardIssuanceRequestDTO request) {
        log.info("Request for card issuance: {}", request);
        return ResponseEntity.ok(cardIssuanceService.issueCard(request));
    }


    @PostMapping(ISSUANCE_STATUS)
    @Operation(summary = "Card Issuance Status", description = "It gives the Status of the Card Issued")
    public ResponseEntity<AppResponseDTO<CardIssuanceResponseDTO>> cardIssuanceStatus(@RequestBody @Valid TransactionStatusRequestDTO request) {
        log.info("Request for Card Issuance Status : {}", request);
        return ResponseEntity.ok(cardIssuanceService.cardIssuanceStatus(request));
    }

    @PostMapping(UPDATE_TOKEN)
    public ResponseEntity<AppResponseDTO> updateTokens(@RequestBody List<String> maskedCardNumbers) {
        decryptionService.updateCardTokens(maskedCardNumbers);
        return ResponseEntity.ok(AppResponseDTO.of(MessageKeyEnum.SUCCESS.getCode(), MessageKeyEnum.SUCCESS, null, null));
    }


}
