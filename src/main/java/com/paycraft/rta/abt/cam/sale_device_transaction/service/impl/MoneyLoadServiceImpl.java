package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MoneyLoadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.TopUpEnquiryDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.CardTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.MoneyLoadService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service

public class MoneyLoadServiceImpl implements MoneyLoadService {
    private final Logger log = LogManager.getLogger(MoneyLoadServiceImpl.class);

    @Autowired
    private CardTransactionClient cardTransactionClient;

    @Autowired
    private DecryptionService decryptionService;

    @Override
    public AppResponseDTO<TopUpEnquiryDTO> getMoneyLoadDetails(EnquiryDTO request) {
        decryptionService.decryptCardDetails(request);
        return cardTransactionClient.getMoneyLoadDetails(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> loadMoney(MoneyLoadRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.loadMoney(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> getMoneyLoadStatus(TransactionStatusRequestDTO request) {
        return cardTransactionClient.getMoneyLoadStatus(request);
    }
}
