package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardAnalysisResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardDetailsDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.CardTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardDetailsService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service

public class CardDetailsServiceImpl implements CardDetailsService {
    private final Logger log = LogManager.getLogger(CardDetailsServiceImpl.class);

    @Autowired
    private CardTransactionClient cardTransactionClient;

    @Autowired
    private DecryptionService decryptionService;

    @Override
    public AppResponseDTO<CardAnalysisResponseDTO> cardAnalysis(EnquiryDTO request) {
        decryptionService.decryptCardDetails(request);
        return cardTransactionClient.cardAnalysis(request);
    }


    @Override
    public AppResponseDTO<CardDetailsDTO> getCardDetails(EnquiryDTO request) {
        decryptionService.decryptCardDetails(request);
        return cardTransactionClient.cardDetails(request);
    }
}
