package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MoneyLoadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.TopUpEnquiryDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.MoneyLoadService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_MONEY_LOAD_DETAILS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.MONEY_LOAD;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.MONEY_LOAD_STATUS_CHECK;

@RestController
public class MoneyLoadController {
    private final Logger log = LogManager.getLogger(MoneyLoadController.class);

    @Autowired
    private MoneyLoadService moneyLoadService;

    @PostMapping(GET_MONEY_LOAD_DETAILS)
    @Operation(summary = "Get Money Load Details", description = "API for enquiry for Money Load Configuration Based on Transit Account and card Type.")
    public ResponseEntity<AppResponseDTO<TopUpEnquiryDTO>> moneyLoadEnquiry(@Valid @RequestBody EnquiryDTO request) {
        log.info("Request for Money Load Enquiry : {}", request);
        return ResponseEntity.ok(moneyLoadService.getMoneyLoadDetails(request));
    }

    @PostMapping(MONEY_LOAD)
    @Operation(summary = "Money Load Api", description = "API for loading money into the Transit Account based on the valid amount given by MONEY_LOAD_ENQUIRY_API.")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> loadMoney(@Valid @RequestBody MoneyLoadRequestDTO moneyLoadRequestDTO) {
        log.info("Request for Load Money into Transit Account : {}", moneyLoadRequestDTO);
        return ResponseEntity.ok(moneyLoadService.loadMoney(moneyLoadRequestDTO));
    }

    @PostMapping(MONEY_LOAD_STATUS_CHECK)
    @Operation(summary = "Money Load Status Api", description = "API for checking the status of money load api.")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> statusCheck(@Valid @RequestBody TransactionStatusRequestDTO request){
        log.info("Request for Money Load Status of specific transit Account");
        return ResponseEntity.ok(moneyLoadService.getMoneyLoadStatus(request));
    }
}
