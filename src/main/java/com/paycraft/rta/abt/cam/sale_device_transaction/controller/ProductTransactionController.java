package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductFamilyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundTransactionDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductTransactionRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.RenewalProductsDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.ProductTransactionService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.script.ScriptException;

import java.util.List;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_ENQUIRY;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_REFUND;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_REFUND_ENQUIRY;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_RENEW;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_RENEWAL_ENQUIRY;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_RENEWAL_STATUS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_SALE;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.PRODUCT_SALE_STATUS;

@Validated
@RestController
public class ProductTransactionController {
    private final Logger log = LogManager.getLogger(ProductTransactionController.class);

    @Autowired
    private ProductTransactionService productTransactionService;


    @PostMapping(PRODUCT_ENQUIRY)
    @Operation(summary = "Card Product Enquiry", description = "It gives provides the Details of the Eligible Products for the specified Card")
    public ResponseEntity<AppResponseDTO<List<ProductFamilyDTO>>> cardProductEnquiry(@Valid @RequestBody EnquiryDTO request) {
        log.info("Request for Product Enquiry : {}", request);
        return ResponseEntity.ok(productTransactionService.cardProductEnquiry(request));
    }

    @PostMapping(PRODUCT_SALE)
    @Operation(
            summary = "Product Sale",
            description = "This endpoint handles the sale of a product. It accepts a product sale request containing product details , processes the transaction, and returns a response with the transaction details."
    )
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> cardProductSale(@Valid @RequestBody ProductTransactionRequestDTO request) {
        log.info("Request for Product Sale : {}", request);
        return ResponseEntity.ok(productTransactionService.productSale(request));
    }

    @PostMapping(PRODUCT_SALE_STATUS)
    @Operation(summary = "Product Sale status", description = "This endpoint retrieves the status of a product sale")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> cardProductSaleStatus(@Valid @RequestBody TransactionStatusRequestDTO request) {
        log.info("Request for Product Sale Status : {}", request);
        return ResponseEntity.ok(productTransactionService.productSaleStatus(request));
    }

    @PostMapping(PRODUCT_RENEWAL_ENQUIRY)
    @Operation(summary = "Product Renewal Enquiry API", description = "This endpoint retrieves the enquiry of product Renewal")
    public ResponseEntity<AppResponseDTO<RenewalProductsDTO>> productRenewalEnquiry(@Valid @RequestBody EnquiryDTO request) {
        log.info("Request for Product Renewal Enquiry: {}", request);
        return ResponseEntity.ok(productTransactionService.productRenewalEnquiry(request));
    }

    @PostMapping(PRODUCT_RENEW)
    @Operation(summary = "Product Renewal", description = "This endpoint is for requesting the Product Renewal.")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> productRenew(@Valid @RequestBody ProductTransactionRequestDTO request) {
        log.info("Request for Product Renewal: {}", request);
        return ResponseEntity.ok(productTransactionService.productRenew(request));
    }

    @PostMapping(PRODUCT_RENEWAL_STATUS)
    @Operation(summary = "Product Renewal status", description = "This endpoint retrieves the status of a product Renewal")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> productRenewalStatus(@Valid @RequestBody TransactionStatusRequestDTO request) {
        log.info("Request for Product Renewal Status : {}", request);
        return ResponseEntity.ok(productTransactionService.productRenewalStatus(request));
    }

    @PostMapping(PRODUCT_REFUND_ENQUIRY)
    @Operation(
            summary = "Enquire Product Refund",
            description = "Fetches the information about product refund"
    )
    public ResponseEntity<AppResponseDTO<ProductRefundEnquiryResponseDTO>> enquireProductRefund(@Valid @RequestBody ProductRefundEnquiryRequestDTO request) throws ScriptException {
        log.info("Enquiry Product Refund.");
        return ResponseEntity.ok(productTransactionService.enquireProductRefund(request));
    }

    @PostMapping(PRODUCT_REFUND)
    @Operation(
            summary = "Product Refund",
            description = "Product Refund"
    )
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> productRefund(@Valid @RequestBody ProductRefundTransactionDTO request) {
        log.info("Product Refund.");
        return ResponseEntity.ok(productTransactionService.productRefund(request));
    }

}
