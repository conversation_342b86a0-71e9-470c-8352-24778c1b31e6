package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TapCardDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceDetailsDTO;

import java.util.List;

public interface DecryptionService {

    void decryptCardDetails(List<CardIssuanceDetailsDTO> tapCardDetailsDTOS);

    void decryptCardDetails(TapCardDetailsDTO tapCardDetailsDTOS);

    void decryptCardDetails(EnquiryDTO enquiryDTO);

    void updateCardTokens(List<String> maskedCardNumbers);
}
