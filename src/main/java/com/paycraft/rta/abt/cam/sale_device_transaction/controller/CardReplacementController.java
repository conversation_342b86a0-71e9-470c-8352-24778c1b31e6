package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementResponseDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardIssuanceService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardReplacementService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_CARD_REPLACEMENT_DETAILS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_CARD_REPLACEMENT_REQUEST_STATUS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.REPLACE_CARD;

@RestController
@Validated
public class CardReplacementController {

    private static final Logger log = LogManager.getLogger(CardReplacementController.class);

    @Autowired
    private CardReplacementService cardReplacementService;

    @Autowired
    private CardIssuanceService cardIssuanceService;


    @PostMapping(GET_CARD_REPLACEMENT_DETAILS)
    @Operation(
            summary = "Enquire Card Replacement details",
            description = "Accepts card replacement enquiry request and provide replacement fee"
    )
    public ResponseEntity<AppResponseDTO<CardReplacementEnquiryResponseDTO>> enquireCardReplacementDetails(@Valid @RequestBody CardReplacementEnquiryRequestDTO request) {
        log.info("Request for Card Replacement Enquiry: {}", request);
        return ResponseEntity.ok(cardReplacementService.getCardReplacementEnquiry(request));
    }


    @PostMapping(REPLACE_CARD)
    @Operation(
            summary = "Card Replacement details",
            description = "Accepts card replacement request and provide replacement details " +
                    "and new replaced card information"
    )
    public ResponseEntity<AppResponseDTO<CardIssuanceResponseDTO>> cardReplacement(@Valid @RequestBody CardReplacementRequestDTO request) {
        log.info("Request for Card Replacement : {}", request);
        return ResponseEntity.ok(cardReplacementService.cardReplacement(request));
    }


    @PostMapping(GET_CARD_REPLACEMENT_REQUEST_STATUS)
    @Operation(
            summary = "Card Replacement Status details",
            description = "Accepts card replacement status request and provide replacement status details "
    )
    public ResponseEntity<AppResponseDTO<CardIssuanceResponseDTO>> cardReplacementStatus(@Valid @RequestBody TransactionStatusRequestDTO request) {
        log.info("Card replacement status request : {}", request);
        return ResponseEntity.ok(cardReplacementService.cardReplacementStatus(request));
    }
}