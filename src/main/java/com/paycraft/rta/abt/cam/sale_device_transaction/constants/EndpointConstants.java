package com.paycraft.rta.abt.cam.sale_device_transaction.constants;

public class EndpointConstants {

    private static final String BASE_URL = "/api/v1";
    public static final String CARD = BASE_URL + "/card";
    public static final String CARD_SALE_CONFIGURATION = CARD + "/enquiry/cardSaleConfig";

    public static final String CARD_ISSUANCE = CARD + "/transaction/issuance";
    public static final String ISSUANCE_STATUS = CARD + "/status/issuance";
    public static final String CARD_ANALYSIS = CARD + "/analysis";
    public static final String GET_CARD_DETAILS = CARD + "/enquiry";
    public static final String REVERSAL_TRANSACTION = CARD + "/transaction/reversal";
    public static final String CARD_REPLACEMENT_ENQUIRY = CARD + "/enquiry/cardReplacement";

    public static final String GET_CARD_REPLACEMENT_DETAILS = CARD + "/enquiry/cardReplacement";
    public static final String REPLACE_CARD = CARD + "/transaction/cardReplacement";
    public static final String GET_CARD_REPLACEMENT_REQUEST_STATUS = CARD + "/status/cardReplacement";

    public static final String GET_MONEY_LOAD_DETAILS = CARD + "/enquiry/moneyLoad";
    public static final String MONEY_LOAD = CARD + "/transaction/moneyLoad";
    public static final String MONEY_LOAD_STATUS_CHECK = CARD + "/status/moneyLoad";

    public static final String MICROPAYMENT_TRANSACTION = CARD + "/transaction/micropayment";
    public static final String MICROPAYMENT_STATUS = CARD + "/status/micropayment";

    public static final String AUTO_RELOAD = CARD + "/service/autoReload";
    public static final String AUTO_RENEWAL = CARD + "/service/autoRenewal";
    public static final String UPDATE_AUTO_RELOAD = CARD + "/service/updateAutoReload";
    public static final String UPDATE_AUTO_RENEWAL = CARD + "/service/updateAutoRenewal";
    public static final String AUTO_RELOAD_ENQUIRY = CARD + "/enquiry/autoReload";
    public static final String AUTO_RENEWAL_ENQUIRY = CARD + "/enquiry/autoRenewal";


    public static final String PRODUCT_REFUND_ENQUIRY = CARD + "/enquiry/refundProduct";
    public static final String PRODUCT_REFUND = CARD + "/transaction/productRefund";
    public static final String PRODUCT_SALE = CARD + "/transaction/product";
    public static final String PRODUCT_SALE_STATUS = CARD + "/status/product";
    public static final String PRODUCT_ENQUIRY = CARD + "/enquiry/product";
    public static final String PRODUCT_RENEWAL_ENQUIRY = CARD + "/enquiry/productRenewal";
    public static final String PRODUCT_RENEW = CARD + "/transaction/productRenewal";
    public static final String PRODUCT_RENEWAL_STATUS = CARD + "/status/productRenewal";


    public static final String GET_TRANSACTION_STATEMENT_FEE_DETAILS = CARD + "/enquiry/statement";
    public static final String TRANSACTION_HISTORY_API = CARD + "/transaction/history";
    public static final String TRANSACTION_STATEMENT = CARD + "/transaction/statement";

    public static final String ACTIVATION_CODE = CARD + "/service/activationCode";
    public static final String ACTIVATE_P_CARD = CARD + "/service/activatePCard";
    public static final String REGISTER_A_CARD = CARD + "/service/aCardRegistration";
    public static final String APPLY_P_CARD = CARD + "/service/pCardApplication";
    public static final String GET_REGISTER_A_CARD_DETAILS = CARD + "/service/aCardRegistration/{cardRegistrationRefNumber}";
    public static final String GET_APPLY_P_CARD_DETAILS = CARD + "/service/pCardApplication/{cardRegistrationRefNumber}";

    public static final String UPDATE_TOKEN = CARD + "/service/updateToken";
    public static final String REVERSAL = CARD + "/transaction/reversal";
    public static final String CARD_UNBLOCK = CARD+"/service/unblockCard";

    public static final String MIGRATION = CARD + "/transaction/migrate";
    public static final String CARD_REFUND = CARD + "/transaction/cardRefund";

}
