package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MicroPaymentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ValidationType;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.MicropaymentService;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.MICROPAYMENT_STATUS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.MICROPAYMENT_TRANSACTION;

@Validated
@RestController
public class MicropaymentController {
    private final Logger log = LogManager.getLogger(MicropaymentController.class);


    @Autowired
    private MicropaymentService micropaymentService;

    /**
     * Processes the micro-payment transaction request and provides the status of the transaction.
     * The request will contain necessary information to process the transaction and return the status.
     */
    @PostMapping(MICROPAYMENT_TRANSACTION)
    @Operation(summary = "Micro payment Transaction", description = "This endpoint processes the micro payment transaction request and provides the status of the transaction.")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> microPaymentTransaction(@RequestBody MicroPaymentRequestDTO request) {
        log.info("Request for Micro payment Transaction Status : {}", request);
        return ResponseEntity.ok(micropaymentService.microPaymentTransaction(request));
    }

    /**
     * Retrieves the status of a micro-payment transaction based on the original transaction identifier.
     * The request contains the transaction identifier to fetch the current status of the micro-payment.
     */
    @PostMapping(MICROPAYMENT_STATUS)
    @Operation(summary = "Micro payment Status", description = "This endpoint retrieves the status of a micro payment transaction based on the original transaction identifier.")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> micropaymentStatus(@RequestBody TransactionStatusRequestDTO request) {
        log.info("Request for Micro payment Status : {}", request);
        return ResponseEntity.ok(micropaymentService.microPaymentStatus(request));

    }
}
