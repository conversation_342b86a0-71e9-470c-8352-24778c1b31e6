package com.paycraft.rta.abt.cam.sale_device_transaction.feign;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductFamilyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundTransactionDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductTransactionRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.RenewalProductsDTO;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(name = "productTransactionClient", url = "http://localhost:9001")
public interface ProductTransactionClient {


    @PostMapping(value = "/product-transaction/v1/product/sale/enquiry", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<List<ProductFamilyDTO>> cardProductEnquiry(EnquiryDTO request);

    @PostMapping(value = "/product-transaction/v1/product/sale", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> productSale(ProductTransactionRequestDTO request);

    @PostMapping(value = "/product-transaction/v1/product/sale/status", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> productSaleStatus(TransactionStatusRequestDTO request);

    @PostMapping(value = "/product-transaction/v1/product/renew/enquiry", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<RenewalProductsDTO> productRenewalEnquiry(EnquiryDTO request);

    @PostMapping(value = "/product-transaction/v1/product/renew", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> productRenew(ProductTransactionRequestDTO request);

    @PostMapping(value = "/product-transaction/v1/product/renew/status", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> productRenewalStatus(TransactionStatusRequestDTO request);

    @PostMapping(value = "/product-transaction/v1/product/refund/enquiry", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<ProductRefundEnquiryResponseDTO> enquireProductRefund(ProductRefundEnquiryRequestDTO request);

    @PostMapping(value = "/product-transaction/v1/product/refund", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> productRefund(ProductRefundTransactionDTO request);
}
