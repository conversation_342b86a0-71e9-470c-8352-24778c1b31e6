package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardSalesDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardConfigurationDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.GeneralTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardConfigService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
public class CardConfigServiceImpl implements CardConfigService {
    private final Logger log = LogManager.getLogger(CardConfigServiceImpl.class);

    @Autowired
    private GeneralTransactionClient generalTransactionClient;

    @Override
    public AppResponseDTO<CardSalesDetailsDTO> getCardConfigurationDetails() {
        return generalTransactionClient.getCardConfigurationDetails();
    }
}
