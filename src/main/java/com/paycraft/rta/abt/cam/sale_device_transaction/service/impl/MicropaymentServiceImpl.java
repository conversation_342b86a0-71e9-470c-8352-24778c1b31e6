package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MicroPaymentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.CardTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.MicropaymentService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class  MicropaymentServiceImpl implements MicropaymentService {

    private final Logger log = LogManager.getLogger(MicropaymentServiceImpl.class);

    @Autowired
    private CardTransactionClient cardTransactionClient;

    @Autowired
    private DecryptionService decryptionService;


    @Override
    public AppResponseDTO<PaymentResponseDTO> microPaymentTransaction(MicroPaymentRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.microPaymentTransaction(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> microPaymentStatus(TransactionStatusRequestDTO request) {
        return cardTransactionClient.getMicroPaymentStatus(request);
    }
}
