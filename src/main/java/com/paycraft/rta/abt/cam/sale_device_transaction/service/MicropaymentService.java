package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MicroPaymentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;

public interface MicropaymentService {


    AppResponseDTO<PaymentResponseDTO> microPaymentTransaction(MicroPaymentRequestDTO request);

    AppResponseDTO<PaymentResponseDTO> microPaymentStatus(TransactionStatusRequestDTO request);
}
