package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardAnalysisResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardDetailsDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.CARD_ANALYSIS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_CARD_DETAILS;

@RestController
public class CardDetailsController {
    private final Logger log = LogManager.getLogger(CardDetailsController.class);

    @Autowired
    private CardDetailsService cardDetailsService;

    @PostMapping(CARD_ANALYSIS)
    @Operation(summary = "Analyze Card Status", description = "Analyzes the card data based on the provided CardAnalysisRequestDTO and returns the analysis response")
    public ResponseEntity<AppResponseDTO<CardAnalysisResponseDTO>> cardAnalysis(
            @Valid @RequestBody EnquiryDTO request) {
        log.info("Request to analyze card: {}", request);
        return ResponseEntity.ok(cardDetailsService.cardAnalysis(request));
    }

    @PostMapping(GET_CARD_DETAILS)
    @Operation(summary = "Enquire Card Details", description = "Fetches the details of the card based on the provided CardEnquiryRequestDTO and returns the card details response")
    public ResponseEntity<AppResponseDTO<CardDetailsDTO>> enquireCardDetails(
            @Valid @RequestBody EnquiryDTO request) {
        log.info("Request to enquire card: {}", request);
        return ResponseEntity.ok(cardDetailsService.getCardDetails(request));
    }
}
