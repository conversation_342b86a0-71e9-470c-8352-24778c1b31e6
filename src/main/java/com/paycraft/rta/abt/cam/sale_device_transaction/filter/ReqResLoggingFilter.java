package com.paycraft.rta.abt.cam.sale_device_transaction.filter;


import com.paycraft.rta.abt.cam.common.auditlogs.AuditLogService;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import static com.paycraft.rta.abt.cam.common.domain.constants.AppConstants.X_CLIENT_REQUEST_IDENTIFIER;


@Component
@Slf4j
public class ReqResLoggingFilter implements Filter {

    @Autowired
    private AuditLogService auditLogServiceImpl;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // Wrap request and response to capture content
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(httpRequest);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(httpResponse);

        // Log request
        logRequest(wrappedRequest);

        // Pass to next filter
        chain.doFilter(wrappedRequest, wrappedResponse);

        // Log response
        logResponse(wrappedRequest, wrappedResponse);

        // Copy response body back to original response
        wrappedResponse.copyBodyToResponse();
    }

    private void logRequest(ContentCachingRequestWrapper request) {
        auditLogServiceImpl.saveRequestAuditLog(request.getRequestURI(), request.getHeader(X_CLIENT_REQUEST_IDENTIFIER), getRequestBody(request));
    }

    private void logResponse(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) {
        auditLogServiceImpl.saveResponseAuditLog(request.getRequestURI(), request.getHeader(X_CLIENT_REQUEST_IDENTIFIER), getResponseBody(response), response.getStatus());
    }

    private String getRequestBody(ContentCachingRequestWrapper request) {
        byte[] content = request.getContentAsByteArray();
        if (content.length == 0) return "";

        try {
            return new String(content, 0, content.length, request.getCharacterEncoding());
        } catch (UnsupportedEncodingException e) {
            return "[unknown encoding]";
        }
    }

    private String getResponseHeaders(HttpServletResponse response) {
        return response.getHeaderNames().stream()
                .map(headerName -> String.format("%s: %s",
                        headerName,
                        response.getHeader(headerName)))
                .reduce((a, b) -> a + "\n" + b)
                .orElse("");
    }

    private String getResponseBody(ContentCachingResponseWrapper response) {
        byte[] content = response.getContentAsByteArray();
        if (content.length == 0) return "";
        try {
            return new String(content, 0, content.length, response.getCharacterEncoding());
        } catch (UnsupportedEncodingException e) {
            return "[unknown encoding]";
        }
    }
}