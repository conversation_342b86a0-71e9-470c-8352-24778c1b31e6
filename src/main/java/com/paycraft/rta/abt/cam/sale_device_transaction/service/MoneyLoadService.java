package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MoneyLoadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.TopUpEnquiryDTO;

public interface MoneyLoadService {

    AppResponseDTO<TopUpEnquiryDTO> getMoneyLoadDetails(EnquiryDTO request);
    AppResponseDTO<PaymentResponseDTO> loadMoney(MoneyLoadRequestDTO request);
    AppResponseDTO<PaymentResponseDTO> getMoneyLoadStatus(TransactionStatusRequestDTO request);
}
