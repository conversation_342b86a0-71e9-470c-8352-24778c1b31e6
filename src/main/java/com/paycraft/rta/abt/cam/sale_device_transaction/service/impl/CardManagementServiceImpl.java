package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.PaymentDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardResponseDto;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.migration.MigrationRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.reversal.ReversalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivatePCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardRequestDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.CardTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardManagementService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CardManagementServiceImpl implements CardManagementService {

    private static final Logger log = LogManager.getLogger(CardManagementServiceImpl.class);

    @Autowired
    private CardTransactionClient cardTransactionClient;

    @Autowired
    private DecryptionService decryptionService;

    @Override
    public AppResponseDTO<ActivationCodeResponseDTO> getActivationCode(ActivationCodeRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.getActivationCode(request);
    }

    @Override
    public AppResponseDTO<TransactionDetailsDTO> activatePCard(ActivatePCardRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.activatePCard(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> registerACard(RegisterACardRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.registerACard(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> applyPCard(ApplyPcardRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.applyPCard(request);
    }

    @Override
    public AppResponseDTO<RegisterACardDetailsResponseDTO> getACardApplicationDetails(String cardRegistrationRefNumber) {
        return cardTransactionClient.getACardApplicationDetails(cardRegistrationRefNumber);
    }

    @Override
    public AppResponseDTO<ApplyPcardDetailsResponseDTO> getPCardApplicationDetails(String cardRegistrationRefNumber) {
        return cardTransactionClient.getPCardApplicationDetails(cardRegistrationRefNumber);
    }

    @Override
    public AppResponseDTO<TransactionDetailsDTO> reverseTransaction(ReversalRequestDTO request) {
        return cardTransactionClient.reverseTransaction(request);
    }

    @Override
    public AppResponseDTO<UnblockCardResponseDto> unblockCard(UnblockCardRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.unblockCard(request);
    }

    @Override
    public AppResponseDTO<CardRefundResponseDTO> cardRefund(CardRefundRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.cardRefund(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> migrate(MigrationRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return cardTransactionClient.cardMigrate(request);
    }
}
