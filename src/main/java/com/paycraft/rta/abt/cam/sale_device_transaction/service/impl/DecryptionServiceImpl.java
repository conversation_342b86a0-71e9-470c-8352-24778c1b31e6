package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;

import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TapCardDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.entities.cards.CamCardTempDataCopy;
import com.paycraft.rta.abt.cam.common.service.CamCardAccountLinkService;
import com.paycraft.rta.abt.cam.common.utils.CommonUtils;
import com.paycraft.rta.abt.cam.common.repository.CamCardTempDataCopyRepository;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DecryptionServiceImpl implements DecryptionService {

    private static final String ICC_DATA_KEY = "iccData";
    private static final String ENC_NON_EMV_CARD_ID_KEY = "encNonEmvCardIds";

    @Autowired
    private CamCardTempDataCopyRepository camCardTempDataCopyRepository;

    @Autowired
    private CamCardAccountLinkService camCardAccountLinkService;

    @Override
    public void decryptCardDetails(List<CardIssuanceDetailsDTO> request) {
        Map<String, List<String>> encKeyMap = getEncKeyMap.apply(request);

        List<CamCardTempDataCopy> camCardTempDataCopyList = camCardTempDataCopyRepository
                .findCamCardDataByConditions(encKeyMap.get(ICC_DATA_KEY), encKeyMap.get(ENC_NON_EMV_CARD_ID_KEY));

        enrichClearCardNos.accept(request, camCardTempDataCopyList);
    }


    @Override
    public void decryptCardDetails(TapCardDetailsDTO tapCardDetailsDTOS) {
       Optional<CamCardTempDataCopy> camCardTempDataCopy = camCardTempDataCopyRepository.findCamCardDataByConditions(tapCardDetailsDTOS.getIccData(), tapCardDetailsDTOS.getNonEmvTagId());
       if (camCardTempDataCopy.isPresent()) {
           tapCardDetailsDTOS.setEmvCardNumber(camCardTempDataCopy.get().getEmvCardNo());
           tapCardDetailsDTOS.setNonEmvTagId(camCardTempDataCopy.get().getNonEmvTagId());
       }
    }

    @Override
    public void decryptCardDetails(EnquiryDTO enquiryDTO) {
        Optional<CamCardTempDataCopy> camCardTempDataCopy = camCardTempDataCopyRepository.findByEncPan(enquiryDTO.getEncPan());
        if (camCardTempDataCopy.isPresent()) {
            enquiryDTO.setEmvCardNumber(camCardTempDataCopy.get().getEmvCardNo());
            enquiryDTO.setNonEmvTagId(camCardTempDataCopy.get().getNonEmvTagId());
        }
    }

    @Override
    public void updateCardTokens(List<String> maskedCardNumbers) {
        Map<String, String> cardNumberAndTokenMap = camCardAccountLinkService.getCardNumberAndTokenMap(maskedCardNumbers);
        List<CamCardTempDataCopy> camCardTempDataCopyList = camCardTempDataCopyRepository.findAllByEmvCardNoIn(new ArrayList<>(cardNumberAndTokenMap.keySet()));
        camCardTempDataCopyList.forEach(camCardTempDataCopy -> {
            camCardTempDataCopy.setCardToken(cardNumberAndTokenMap.get(camCardTempDataCopy.getEmvCardNo()));
        });
        camCardTempDataCopyRepository.saveAll(camCardTempDataCopyList);
    }

    private Function<TapCardDetailsDTO, String> getTapCardKey = (tapCardDetailsDTO) -> CommonUtils.coalesce(tapCardDetailsDTO.getIccData(), tapCardDetailsDTO.getEncNonEmvCardId());
    private Function<CamCardTempDataCopy, String> getEncKey = (camCardTempDataCopy) -> CommonUtils.coalesce(camCardTempDataCopy.getIccData(), camCardTempDataCopy.getEncNonEmvCardId(), camCardTempDataCopy.getEncPan());

    private BiConsumer<List<CardIssuanceDetailsDTO>, List<CamCardTempDataCopy>> enrichClearCardNos = ((tapCardDetailsDTOS, camCardTempDataCopies) -> {
        tapCardDetailsDTOS.forEach(tapCardDetailsDTO -> {
            camCardTempDataCopies.forEach(camCardTempDataCopy -> {
                        if (getEncKey.apply(camCardTempDataCopy).equals(getTapCardKey.apply(tapCardDetailsDTO))) {
                            tapCardDetailsDTO.setEmvCardNumber(camCardTempDataCopy.getEmvCardNo());
                            tapCardDetailsDTO.setNonEmvTagId(camCardTempDataCopy.getNonEmvTagId());
                        }
                    });
                }
        );
    });



    private Function<List<CardIssuanceDetailsDTO>, Map<String, List<String>>> getEncKeyMap = (tapCardDetailsDTOs -> {
        return tapCardDetailsDTOs.stream()
                .filter(Objects::nonNull)
                .flatMap(tapCardDetailsDTO -> Stream.of(
                        new AbstractMap.SimpleEntry<>(ICC_DATA_KEY, tapCardDetailsDTO.getIccData()),
                        new AbstractMap.SimpleEntry<>(ENC_NON_EMV_CARD_ID_KEY, tapCardDetailsDTO.getEncNonEmvCardId())
                ))
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
    });
}
