package com.paycraft.rta.abt.cam.sale_device_transaction;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = {"com.paycraft.rta.abt.cam.sale_device_transaction", "com.paycraft.rta.abt.cam.common"})
@EnableJpaRepositories(basePackages = "com.paycraft.rta.abt.cam.common.repository")
@EntityScan(basePackages = "com.paycraft.rta.abt.cam.common.domain.entities")
@EnableFeignClients
@EnableAsync
public class SaleDeviceTransaction {

    public static void main(String[] args) {
        SpringApplication.run(SaleDeviceTransaction.class, args);
    }

}