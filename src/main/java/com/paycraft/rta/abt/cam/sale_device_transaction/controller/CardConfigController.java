package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardSalesDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardConfigurationDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardConfigService;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.CARD_SALE_CONFIGURATION;

@Validated
@RestController
public class CardConfigController {
    private final Logger log = LogManager.getLogger(CardConfigController.class);

    @Autowired
    private CardConfigService cardConfigService;

    /**
     * Controller method to handle the enquiry request for card sale configurations.
     * <p>
     * This endpoint allows clients to query the current configuration settings for card sales.
     * It fetches a list of card sale configurations from the service layer and returns the result in the response.
     *
     * @return ResponseEntity containing a list of CardSaleConfigurationDTO objects wrapped in an DeprecatedAppResponseDTO.
     * If the request is successful, it returns a 200 OK response with the list of configurations.
     */
    @GetMapping(CARD_SALE_CONFIGURATION)
    @Operation(
            summary = "Enquire Card Sale Configuration",
            description = "Fetches the list of configurations for card sales. This endpoint allows clients to retrieve the current card sale configuration details."
    )
    public ResponseEntity<AppResponseDTO<CardSalesDetailsDTO>> enquireCardSaleConfiguration() {
        log.info("Enquiry for card sale configuration started.");
        return ResponseEntity.ok(cardConfigService.getCardConfigurationDetails());
    }

}
