package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;

public interface CardIssuanceService {

    AppResponseDTO<CardIssuanceResponseDTO> issueCard(CardIssuanceRequestDTO request);

    AppResponseDTO<CardIssuanceResponseDTO> cardIssuanceStatus(TransactionStatusRequestDTO request);
}