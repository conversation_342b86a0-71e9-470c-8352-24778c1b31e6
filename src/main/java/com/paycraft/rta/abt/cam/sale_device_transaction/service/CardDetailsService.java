package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardAnalysisResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardDetailsDTO;

public interface CardDetailsService {

    AppResponseDTO<CardAnalysisResponseDTO> cardAnalysis(EnquiryDTO request);
    AppResponseDTO<CardDetailsDTO> getCardDetails(EnquiryDTO request);
}
