package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductFamilyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundTransactionDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductTransactionRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.RenewalProductsDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.ProductTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.ProductTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.logging.Logger;

@Service
public class ProductTransactionServiceImpl implements ProductTransactionService {

    private static final Logger log = Logger.getLogger(ProductTransactionServiceImpl.class.getName());

    @Autowired
    private ProductTransactionClient productTransactionClient;

    @Autowired
    private DecryptionService decryptionService;


    @Override
    public AppResponseDTO<List<ProductFamilyDTO>> cardProductEnquiry(EnquiryDTO request) {
        decryptionService.decryptCardDetails(request);
        return productTransactionClient.cardProductEnquiry(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> productSale(ProductTransactionRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return productTransactionClient.productSale(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> productSaleStatus(TransactionStatusRequestDTO request) {
        return productTransactionClient.productSaleStatus(request);
    }

    @Override
    public AppResponseDTO<RenewalProductsDTO> productRenewalEnquiry(EnquiryDTO request) {
        decryptionService.decryptCardDetails(request);
        return productTransactionClient.productRenewalEnquiry(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> productRenew(ProductTransactionRequestDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return productTransactionClient.productRenew(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> productRenewalStatus(TransactionStatusRequestDTO request) {
        return productTransactionClient.productRenewalStatus(request);
    }

    @Override
    public AppResponseDTO<ProductRefundEnquiryResponseDTO> enquireProductRefund(ProductRefundEnquiryRequestDTO request) {
        decryptionService.decryptCardDetails(request);
        return productTransactionClient.enquireProductRefund(request);
    }

    @Override
    public AppResponseDTO<PaymentResponseDTO> productRefund(ProductRefundTransactionDTO request) {
        decryptionService.decryptCardDetails(request.getCardDetails());
        return productTransactionClient.productRefund(request);
    }
}
