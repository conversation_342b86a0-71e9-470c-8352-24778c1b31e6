package com.paycraft.rta.abt.cam.sale_device_transaction.feign;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.PaymentDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardResponseDto;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.migration.MigrationRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.reversal.ReversalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardAnalysisResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivatePCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MicroPaymentRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.MoneyLoadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.TopUpEnquiryDTO;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "cardTransactionClient", url = "http://localhost:9000")
public interface CardTransactionClient {

    @PostMapping(value = "/card-transaction/v1/card/issuance", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardIssuanceResponseDTO> issueCard(CardIssuanceRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/issuance/status", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardIssuanceResponseDTO> issueCardStatus(TransactionStatusRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/analysis/details", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardAnalysisResponseDTO> cardAnalysis(EnquiryDTO request);

    @PostMapping(value = "/card-transaction/v1/card/details", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardDetailsDTO> cardDetails(EnquiryDTO request);

    @PostMapping(value = "/card-transaction/v1/card/moneyLoad/details", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TopUpEnquiryDTO> getMoneyLoadDetails(EnquiryDTO request);

    @PostMapping(value = "/card-transaction/v1/card/moneyLoad", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> loadMoney(MoneyLoadRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/moneyLoad/status", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> getMoneyLoadStatus(TransactionStatusRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/micro-payment", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> microPaymentTransaction(MicroPaymentRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/micro-payment/status", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> getMicroPaymentStatus(TransactionStatusRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/replacement/details", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardReplacementEnquiryResponseDTO> getCardReplacementEnquiry(CardReplacementEnquiryRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/replacement", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardIssuanceResponseDTO> cardReplacement(CardReplacementRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/replacement/status", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardIssuanceResponseDTO> cardReplacementStatus(TransactionStatusRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/activation-code", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<ActivationCodeResponseDTO> getActivationCode(ActivationCodeRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/p-card/activate", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TransactionDetailsDTO> activatePCard(ActivatePCardRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/a-card/register", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> registerACard(RegisterACardRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/p-card/apply", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> applyPCard(ApplyPcardRequestDTO request);

    @GetMapping(value = "/card-transaction/v1/card/a-card/application/details/{cardRegistrationRefNumber}", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<RegisterACardDetailsResponseDTO> getACardApplicationDetails(@RequestParam("cardRegistrationRefNumber") String cardRegistrationRefNumber);

    @GetMapping(value = "/card-transaction/v1/card/p-card/application/details/{cardRegistrationRefNumber}", consumes = "application/json")
    @Headers("Content-Type: appcliation/json")
    AppResponseDTO<ApplyPcardDetailsResponseDTO> getPCardApplicationDetails(@RequestParam("cardRegistrationRefNumber") String cardRegistrationRefNumber);

    @PostMapping(value = "/card-transaction/v1/card/transaction/reversal", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<TransactionDetailsDTO> reverseTransaction(ReversalRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/unblock", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<UnblockCardResponseDto> unblockCard(UnblockCardRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/refund", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<CardRefundResponseDTO> cardRefund(CardRefundRequestDTO request);

    @PostMapping(value = "/card-transaction/v1/card/migration", consumes = "application/json")
    @Headers("Content-Type: application/json")
    AppResponseDTO<PaymentResponseDTO> cardMigrate(MigrationRequestDTO request);
}
