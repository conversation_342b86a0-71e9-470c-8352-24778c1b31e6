package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementResponseDTO;

public interface CardReplacementService {

    AppResponseDTO<CardReplacementEnquiryResponseDTO> getCardReplacementEnquiry(CardReplacementEnquiryRequestDTO request);

    AppResponseDTO<CardIssuanceResponseDTO> cardReplacement(CardReplacementRequestDTO request);

    AppResponseDTO<CardIssuanceResponseDTO> cardReplacementStatus(TransactionStatusRequestDTO request);
}
