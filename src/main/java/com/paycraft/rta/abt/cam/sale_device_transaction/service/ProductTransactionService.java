package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductFamilyDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductRefundTransactionDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.ProductTransactionRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.products.RenewalProductsDTO;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface ProductTransactionService {

    AppResponseDTO<List<ProductFamilyDTO>> cardProductEnquiry(EnquiryDTO request);

    AppResponseDTO<PaymentResponseDTO> productSale(ProductTransactionRequestDTO request);

    AppResponseDTO<PaymentResponseDTO> productSaleStatus(TransactionStatusRequestDTO request);

    AppResponseDTO<RenewalProductsDTO> productRenewalEnquiry(EnquiryDTO request);

    AppResponseDTO<PaymentResponseDTO> productRenew(ProductTransactionRequestDTO request);

    AppResponseDTO<PaymentResponseDTO> productRenewalStatus(TransactionStatusRequestDTO request);

    AppResponseDTO<ProductRefundEnquiryResponseDTO> enquireProductRefund(ProductRefundEnquiryRequestDTO request);

    AppResponseDTO<PaymentResponseDTO> productRefund(ProductRefundTransactionDTO request);
}
