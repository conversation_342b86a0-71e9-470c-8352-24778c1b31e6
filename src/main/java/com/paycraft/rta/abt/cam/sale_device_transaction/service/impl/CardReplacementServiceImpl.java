package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionStatusRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardIssuanceResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.replacement.CardReplacementResponseDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.feign.CardTransactionClient;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardReplacementService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CardReplacementServiceImpl implements CardReplacementService {

    private static final Logger log = LogManager.getLogger(CardReplacementServiceImpl.class);

    @Autowired
    private CardTransactionClient cardTransactionClient;

    @Autowired
    private DecryptionService decryptionService;

    @Override
    public AppResponseDTO<CardReplacementEnquiryResponseDTO> getCardReplacementEnquiry(CardReplacementEnquiryRequestDTO request) {
        return cardTransactionClient.getCardReplacementEnquiry(request);
    }

    @Override
    public AppResponseDTO<CardIssuanceResponseDTO> cardReplacement(CardReplacementRequestDTO request)  {
        decryptionService.decryptCardDetails(request.getNewCardDetails());
        return cardTransactionClient.cardReplacement(request);
    }

    @Override
    public AppResponseDTO<CardIssuanceResponseDTO> cardReplacementStatus(TransactionStatusRequestDTO request) {
        return cardTransactionClient.cardReplacementStatus(request);
    }
}
