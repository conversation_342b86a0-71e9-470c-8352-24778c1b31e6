package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.PaymentDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardResponseDto;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.migration.MigrationRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.reversal.ReversalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivatePCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardRequestDTO;
import jakarta.validation.Valid;

public interface CardManagementService {


 AppResponseDTO<ActivationCodeResponseDTO> getActivationCode(@Valid ActivationCodeRequestDTO request);

 AppResponseDTO<TransactionDetailsDTO> activatePCard(@Valid ActivatePCardRequestDTO request);

 AppResponseDTO<PaymentResponseDTO> registerACard(@Valid RegisterACardRequestDTO request);

 AppResponseDTO<PaymentResponseDTO> applyPCard(@Valid ApplyPcardRequestDTO request);

 AppResponseDTO<RegisterACardDetailsResponseDTO> getACardApplicationDetails(@Valid String cardRegistrationRefNumber);

 AppResponseDTO<ApplyPcardDetailsResponseDTO> getPCardApplicationDetails(String cardRegistrationRefNumber);

 AppResponseDTO<TransactionDetailsDTO> reverseTransaction(@Valid ReversalRequestDTO request);

 AppResponseDTO<UnblockCardResponseDto> unblockCard(UnblockCardRequestDTO request);

 AppResponseDTO<CardRefundResponseDTO> cardRefund(CardRefundRequestDTO request);

 AppResponseDTO<PaymentResponseDTO> migrate(MigrationRequestDTO request);
}
