package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.PaymentDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementRequestDTO;

public interface GeneralTransactionService {
    AppResponseDTO<AutoReloadRenewalResponseDTO> processAutoReload(AutoReloadRequestDTO request);

    AppResponseDTO<TransactionDetailsDTO> updateAutoReloadSetting(UpdateAutoReloadRequestDTO request);

    AppResponseDTO<AutoReloadRenewalResponseDTO> processAutoRenewal(AutoRenewalRequestDTO request);

    AppResponseDTO<TransactionDetailsDTO> updateAutoRenewalSetting(UpdateAutoRenewalRequestDTO request);

    AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO> getAutoReloadSettingDetails(EnquiryDTO request);

    AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO> getAutoRenewalSettingDetails(EnquiryDTO request);

    AppResponseDTO<TransactionStatementEnquiryResponseDTO> getTransactionFeeDetails(TransactionStatementEnquiryRequestDTO request);

    AppResponseDTO<TransactionHistoryResponseDTO> getTransactionHistory(TransactionHistoryRequestDTO request);

    AppResponseDTO<PaymentResponseDTO> getTransactionStatement(TransactionStatementRequestDTO request);
}
