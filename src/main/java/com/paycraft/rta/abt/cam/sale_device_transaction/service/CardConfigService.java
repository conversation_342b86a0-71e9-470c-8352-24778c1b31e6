package com.paycraft.rta.abt.cam.sale_device_transaction.service;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.details.CardSalesDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.issuance.CardConfigurationDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.CardSaleActionEnum;

import java.util.List;

public interface CardConfigService {

    AppResponseDTO<CardSalesDetailsDTO>  getCardConfigurationDetails();
}
