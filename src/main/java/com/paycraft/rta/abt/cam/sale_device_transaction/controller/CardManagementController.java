package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.PaymentDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.CardRefundResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.UnblockCardResponseDto;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.migration.MigrationRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.reversal.ReversalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.acard.RegisterACardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivatePCardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ActivationCodeResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardDetailsResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.cards.pcard.ApplyPcardRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardManagementService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.ACTIVATE_P_CARD;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.ACTIVATION_CODE;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.APPLY_P_CARD;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.CARD_REFUND;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.CARD_UNBLOCK;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_APPLY_P_CARD_DETAILS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_REGISTER_A_CARD_DETAILS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.MIGRATION;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.REGISTER_A_CARD;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.REVERSAL;

@Validated
@RestController
public class CardManagementController {

    private static final Logger log = LogManager.getLogger(CardManagementController.class);

    @Autowired
    private CardManagementService cardManagementService;

    @PostMapping(ACTIVATION_CODE)
    @Operation(summary = "P Card Activation Code", description = "API TO provide the AuthCode, which is used at time to Activate the Card.")
    public ResponseEntity<AppResponseDTO<ActivationCodeResponseDTO>> activationCode(@Valid @RequestBody ActivationCodeRequestDTO request){
        log.info("request for ActivationCode : {}",request);
        return ResponseEntity.ok(cardManagementService.getActivationCode(request));
    }


    @PostMapping(ACTIVATE_P_CARD)
    @Operation(summary = "Activate Personalized Card", description = "Personalized card status to be changed to ACTIVE")
    public ResponseEntity<AppResponseDTO<TransactionDetailsDTO>> activatePersonalizedCard(@Valid @RequestBody ActivatePCardRequestDTO request){
        log.info("request for Activate-P-Card : {}",request);
        return ResponseEntity.ok(cardManagementService.activatePCard(request));
    }


    @PostMapping(REGISTER_A_CARD)
    @Operation(summary = "Register A-Card", description = "Registration of Anonymous Card")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> registerACard(@Valid @RequestBody RegisterACardRequestDTO request){
        log.info("request for Register-A-Card : {}",request);
        return ResponseEntity.ok(cardManagementService.registerACard(request));
    }


    @PostMapping(APPLY_P_CARD)
    @Operation(summary = "Apply Personalized Card", description = "Applying for the Personalized Card")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> applyPCard(@Valid @RequestBody ApplyPcardRequestDTO request){
        log.info("Applying for Personalized-Card : {}",request);
        return ResponseEntity.ok(cardManagementService.applyPCard(request));
    }


    @GetMapping(GET_REGISTER_A_CARD_DETAILS)
    @Operation(summary = "Fetch Details of A-Card Registration", description = "Fetch A-Card Registration details by 'CardRegistrationRefNumber'")
    public ResponseEntity<AppResponseDTO<RegisterACardDetailsResponseDTO>> getAcardApplicationDetails(@Valid @PathVariable String cardRegistrationRefNumber){
        log.info("request for fetch Register-A-Card-Details: {}",cardRegistrationRefNumber);
        return ResponseEntity.ok(cardManagementService.getACardApplicationDetails(cardRegistrationRefNumber));
    }

    @GetMapping(GET_APPLY_P_CARD_DETAILS)
    @Operation(summary = "Fetch Application of Personalized Card", description = "Fetch Details of Application for Personalized Card")
    public ResponseEntity<AppResponseDTO<ApplyPcardDetailsResponseDTO>> getPCardApplicationDetails(@PathVariable String cardRegistrationRefNumber){
        log.info("Fetch Application for Personalized-Card : {}",cardRegistrationRefNumber);
        return ResponseEntity.ok(cardManagementService.getPCardApplicationDetails(cardRegistrationRefNumber));
    }


    @PostMapping(REVERSAL)
    @Operation(summary = "Reversal API for RollBack", description = "Reversal API to rollBack all the transactions")
    public ResponseEntity<AppResponseDTO<TransactionDetailsDTO>> reversalTransaction(@Valid @RequestBody ReversalRequestDTO request){
        log.info("request for Reversal Transaction API : {}",request);
        return ResponseEntity.ok(cardManagementService.reverseTransaction(request));
    }

    @PostMapping(CARD_UNBLOCK)
    @Operation(summary = "Unblock the Card", description = "Unblock the Temporarily Hotlisted card")
    public ResponseEntity<AppResponseDTO<UnblockCardResponseDto>> unblockCard(@RequestBody UnblockCardRequestDTO request){
        log.info("Unblock Temporarily Blocked/Suspended card");
        return ResponseEntity.ok(cardManagementService.unblockCard(request));
    }

    @PostMapping(CARD_REFUND)
    @Operation(summary = "Card Refund", description = "card refund")
    public ResponseEntity<AppResponseDTO<CardRefundResponseDTO>> refundCard(@RequestBody CardRefundRequestDTO request){
        log.info("Card Refund.");
        return ResponseEntity.ok(cardManagementService.cardRefund(request));
    }

    @PostMapping(MIGRATION)
    @Operation(summary = "Card Migration", description = "card migration")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> cardMigration(@RequestBody MigrationRequestDTO request){
        log.info("Card Refund.");
        return ResponseEntity.ok(cardManagementService.migrate(request));
    }


}
