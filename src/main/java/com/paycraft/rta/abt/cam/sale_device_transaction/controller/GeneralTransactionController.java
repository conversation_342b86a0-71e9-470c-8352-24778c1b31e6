package com.paycraft.rta.abt.cam.sale_device_transaction.controller;


import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.EnquiryDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.TransactionDetailsDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.payments.PaymentResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRenewalResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.AutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoReloadRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.renewalandreload.UpdateAutoRenewalRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionHistoryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryRequestDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementEnquiryResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.transactions.TransactionStatementRequestDTO;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.DecryptionService;
import com.paycraft.rta.abt.cam.sale_device_transaction.service.GeneralTransactionService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.AUTO_RELOAD;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.AUTO_RELOAD_ENQUIRY;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.AUTO_RENEWAL;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.AUTO_RENEWAL_ENQUIRY;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.GET_TRANSACTION_STATEMENT_FEE_DETAILS;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.TRANSACTION_HISTORY_API;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.TRANSACTION_STATEMENT;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.UPDATE_AUTO_RELOAD;
import static com.paycraft.rta.abt.cam.sale_device_transaction.constants.EndpointConstants.UPDATE_AUTO_RENEWAL;

@RestController
public class GeneralTransactionController {
    private final Logger log = LogManager.getLogger(GeneralTransactionController.class);

    @Autowired
    private GeneralTransactionService generalTransactionService;

    @Autowired
    private DecryptionService decryptionService;

    @PostMapping(AUTO_RELOAD)
    @Operation(summary = "Process Auto Reload", description = "Processes the Auto Reload and returns the response")
    public ResponseEntity<AppResponseDTO<AutoReloadRenewalResponseDTO>> processAutoReload(
            @RequestBody AutoReloadRequestDTO autoReloadRequestDTO) {
        log.info("Request to process auto reload: {}", autoReloadRequestDTO);
        decryptionService.decryptCardDetails(autoReloadRequestDTO.getCardDetails());
        return ResponseEntity.ok(generalTransactionService.processAutoReload(autoReloadRequestDTO));
    }

    /**
     * Update Auto Reload: autoReloadAction
     * This API is used to update the auto reload option for the service.
     * this action is defined in request as "request.autoReloadAction" (suspend/ cancel/ resume).
     * will go to that card service through "card Data" coming in request.
     * and change the "IS_AUTO_TOP_UP_ENABLED" to as per request.
     */
    @PostMapping(UPDATE_AUTO_RELOAD)
    @Operation(summary = "Update Auto Reload", description = "This API is used to update the auto reload option for the service")
    public ResponseEntity<AppResponseDTO<TransactionDetailsDTO>> updateAutoReload(@Valid @RequestBody UpdateAutoReloadRequestDTO request) {
        log.info("Request for Update_Auto_Reload: {}", request);
        decryptionService.decryptCardDetails(request.getCardDetails());
        return ResponseEntity.ok(generalTransactionService.updateAutoReloadSetting(request));
    }


    @PostMapping(AUTO_RENEWAL)
    @Operation(summary = "Process Auto Renewal", description = "Processes the Auto Renewal request and returns the response")
    public ResponseEntity<AppResponseDTO<AutoReloadRenewalResponseDTO>> processAutoRenewal(@RequestBody AutoRenewalRequestDTO request) {
        log.info("Request to process auto_renewal: {}", request);
        decryptionService.decryptCardDetails(request.getCardDetails());
        return ResponseEntity.ok(generalTransactionService.processAutoRenewal(request));
    }

    @PostMapping(UPDATE_AUTO_RENEWAL)
    @Operation(summary = "Update Auto Renewal", description = "Updates the Auto Renewal settings for the specified Card")
    public ResponseEntity<AppResponseDTO<TransactionDetailsDTO>> updateAutoRenewal(@Valid @RequestBody UpdateAutoRenewalRequestDTO request) {
        log.info("Request for Update_Auto_Reload: {}", request);
        decryptionService.decryptCardDetails(request.getCardDetails());
        return ResponseEntity.ok(generalTransactionService.updateAutoRenewalSetting(request));
    }


    /**
     * Get Transaction Fee Details: getTransactionFeeDetails
     * This API is used to fetch the fee details for a given transaction statement.
     * This action is defined in the request as "request.transactionStatementEnquiry" and will fetch
     * the associated fee information from the service.
     */
    @PostMapping(GET_TRANSACTION_STATEMENT_FEE_DETAILS)
    @Operation(summary = "Transaction Fee Details Enquiry", description = "This provides the fee details for a given transaction statement.")
    public ResponseEntity<AppResponseDTO<TransactionStatementEnquiryResponseDTO>> getTransactionFeeDetails(@Valid @RequestBody TransactionStatementEnquiryRequestDTO request) {
        log.info("Request for GET_TRANSACTION_STATEMENT_FEE_DETAILS: {}", request);
        decryptionService.decryptCardDetails(request);
        return ResponseEntity.ok(generalTransactionService.getTransactionFeeDetails(request));
    }

    /**
     * Get Transaction History: getTransactionHistory
     * This API is used to fetch the transaction history details for a given transaction request.
     * This action is defined in the request body and fetches the transaction history details from the service.
     */
    @PostMapping(TRANSACTION_HISTORY_API)
    @Operation(summary = "Transaction History for card account number", description = "This provides the transaction history for a given transaction request.")
    public ResponseEntity<AppResponseDTO<TransactionHistoryResponseDTO>> getTransactionHistory(@Valid @RequestBody TransactionHistoryRequestDTO request) {
        log.info("Request for GET_TRANSACTION_HISTORY: {}", request);
        decryptionService.decryptCardDetails(request.getCardDetails());
        return ResponseEntity.ok(generalTransactionService.getTransactionHistory(request));
    }

    /**
     * Get Transaction Statement: getTransactionStatement
     * This API is used to fetch the transaction statement details for a given transaction request.
     * This action is defined in the request body and fetches the transaction statement details from the service.
     */
    @PostMapping(TRANSACTION_STATEMENT)
    @Operation(summary = "Transaction Statement for card account", description = "This provides the transaction statement for a given transaction request.")
    public ResponseEntity<AppResponseDTO<PaymentResponseDTO>> getTransactionStatement(@RequestBody TransactionStatementRequestDTO request) {
        log.info("Request for TRANSACTION_STATEMENT: {}", request);
        decryptionService.decryptCardDetails(request.getCardDetails());
        return ResponseEntity.ok(generalTransactionService.getTransactionStatement(request));
    }

    @PostMapping(AUTO_RELOAD_ENQUIRY)
    @Operation(summary = "Auto Reload Enquiry", description = "This API is used to fetch the auto reload status based on the card account number")
    public ResponseEntity<AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO>> autoReloadEnquiry(@Valid @RequestBody EnquiryDTO request) {
        log.info("Request for Auto_Reload_Enquiry: {}", request);
        decryptionService.decryptCardDetails(request);
        return ResponseEntity.ok(generalTransactionService.getAutoReloadSettingDetails(request));
    }

    @PostMapping(AUTO_RENEWAL_ENQUIRY)
    @Operation(summary = "Auto Reload Enquiry", description = "This API is used to fetch the auto renewal status based on the card account number")
    public ResponseEntity<AppResponseDTO<AutoReloadRenewalEnquiryResponseDTO>> autoRenewalEnquiry(@Valid @RequestBody EnquiryDTO request) {
        log.info("Request for Auto_Renewal_Enquiry: {}", request);
        decryptionService.decryptCardDetails(request);
        return ResponseEntity.ok(generalTransactionService.getAutoRenewalSettingDetails(request));
    }
}
