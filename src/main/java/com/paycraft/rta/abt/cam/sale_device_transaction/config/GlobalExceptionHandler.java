package com.paycraft.rta.abt.cam.sale_device_transaction.config;

import com.paycraft.rta.abt.cam.common.domain.dtos.AppResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.dtos.ValidationResponseDTO;
import com.paycraft.rta.abt.cam.common.domain.enums.ErrorCodesEnum;
import com.paycraft.rta.abt.cam.common.domain.enums.MessageKeyEnum;
import com.paycraft.rta.abt.cam.common.exception.ResourceNotFoundException;
import com.paycraft.rta.abt.cam.common.exception.ValidationException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.List;
import java.util.stream.Collectors;

@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {


    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        // Extract validation errors
        List<ValidationResponseDTO> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(fieldError -> ValidationResponseDTO.of(
                        HttpStatus.BAD_REQUEST.value(),
                        fieldError.getDefaultMessage()
                ))
                .collect(Collectors.toList());

        // Construct AppResponseDTO with validation errors
        AppResponseDTO response = AppResponseDTO.of(
                String.valueOf(HttpStatus.BAD_REQUEST.value()),
                "Validation failed",
                errors, null // Passing list of validation errors
        );
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<AppResponseDTO> handleGeneralException(Exception ex, WebRequest request) {
        ex.printStackTrace();
        var response = new AppResponseDTO(MessageKeyEnum.VALIDATION_FAILED.getCode(), MessageKeyEnum.VALIDATION_FAILED.getMessage(), null, null);
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<AppResponseDTO> handleValidationException1(ValidationException ex, WebRequest request) {
        MessageKeyEnum messageKey = MessageKeyEnum.VALIDATION_FAILED;
        List<ValidationResponseDTO> errors = ex.getValidationErrors();
        AppResponseDTO response = new AppResponseDTO(messageKey.getCode(),messageKey.getMessage(),errors, null);
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles ResourceNotFoundException when a requested resource is not found.
     * Returns a response indicating the resource was not found.
     *
     * @param ex      The caught ResourceNotFoundException.
     * @param request The web request object.
     * @return A ResponseEntity with resource not found error details.
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<AppResponseDTO> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        MessageKeyEnum messageKey = MessageKeyEnum.RESOURCE_NOT_FOUND;
        AppResponseDTO appResponseDTO = new AppResponseDTO(messageKey.getCode(), messageKey.getMessage(),List.of(ValidationResponseDTO.of(ErrorCodesEnum.RESOURCE_NOT_FOUND.getErrorCode(), ErrorCodesEnum.RESOURCE_NOT_FOUND.getMessage())), null);
        return new ResponseEntity<>(appResponseDTO, HttpStatus.NOT_FOUND);
    }

    /*
    * for handling the Repository level exceptions.
    * */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<AppResponseDTO> handleRepositoryException(DataIntegrityViolationException ex, WebRequest request) {
        MessageKeyEnum messageKey = MessageKeyEnum.VALIDATION_FAILED;
        AppResponseDTO appResponseDTO = new AppResponseDTO(messageKey.getCode(), messageKey.getMessage(),List.of(ValidationResponseDTO.of(ErrorCodesEnum.INVALID_REQUEST.getErrorCode(), ex.getMessage())), null);
        return new ResponseEntity<>(appResponseDTO, HttpStatus.NOT_FOUND);
    }
}