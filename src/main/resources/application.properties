spring.application.name=sale-device-transactions
server.port=8083
spring.profiles.active=dev

#Health Management
management.endpoints.web.exposure.include=*

# Oracle DataSource Configuration
#spring.datasource.url=********************************************************
spring.datasource.url=jdbc:oracle:thin:@//${DB_URL}/${DB_NAME}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Spring Doc
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.path=/swagger/sale-device-transactions/v3/api-docs
springdoc.swagger-ui.path=/swagger/sale-device-transactions/index.html

# Logging
logging.level.com.paycraft.rta.abt.cam.sale_device_transaction.client.ProductTransactionClient=INFO
logging.level.feign=DEBUG
logging.level.org.springframework.cloud.openfeign=DEBUG

feign.codec.decoder=feign.codec.Decoder
feign.codec.encoder=feign.codec.Encoder
feign.client.config.default.loggerLevel=full


