//package com.paycraft.rta.abt.cam.sale_device_transaction.service.impl;
//
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardAnalysisRequestDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardAnalysisResponseDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardDetailViewDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardEnquiryResponseDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardSaleConfigurationDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardSalesDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.EnquiryCardDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.FareMediaDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.payments.LoadMoneyEnquiryResponseDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.products.ProductDto;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.payments.TopUpEnquiryDataDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.entities.CustomerDetailsView;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.entities.CardProductMaster;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.entities.ProductView;
//import com.paycraft.rta.abt.cam.sale_device_transaction.mapper.CustomerDetailsViewMapper;
//import com.paycraft.rta.abt.cam.sale_device_transaction.mapper.CardProductMasterMapper;
//import com.paycraft.rta.abt.cam.sale_device_transaction.mapper.ProductViewMapper;
//import com.paycraft.rta.abt.cam.sale_device_transaction.repository.CardAccountDetailsRepository;
//import com.paycraft.rta.abt.cam.sale_device_transaction.repository.CustomerDetailsRepository;
//import com.paycraft.rta.abt.cam.sale_device_transaction.repository.CardProductMasterRepository;
//import com.paycraft.rta.abt.cam.sale_device_transaction.repository.ProductViewRepository;
//import com.paycraft.rta.abt.cam.sale_device_transaction.service.EncryptionDecryptionService;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertNull;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.Mockito.any;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//
//@ExtendWith(MockitoExtension.class)
//public class SaleDeviceTransactionsServiceImplTestController {
//
//    @Mock
//    private CardProductMasterRepository fareMediaRepository;
//    @Mock
//    private CardProductMasterMapper fareMediaMapper;
//
//    @InjectMocks
//    private CardIssuanceServiceImpl saleDeviceTransactionsService;
//
//    @Mock
//    private CardAccountDetailsRepository cardAccountDetailsRepository;
//    @Mock
//    private ProductViewMapper productMapper;
//
//    @Mock
//    private CustomerDetailsViewMapper customerDetailsViewMapper;
//    @Mock
//    private EncryptionDecryptionService decryptionService;
//    @Mock
//    private ProductViewRepository productRepository;
//    @Mock
//    private CustomerDetailsRepository customerDetailsRepository;
//
//
//    FareMediaDTO fareMediaDTO;
//
//    TopUpEnquiryDataDTO topUpEnquiryDataDTO = TopUpEnquiryDataDTO.builder()
//            .minTopUp("20")
//            .balance("960")
//            .debt("20")
//            .maxTopUp("20")
//            .build();
//
//    LoadMoneyEnquiryResponseDTO loadMoneyEnquiryResponseDTO = LoadMoneyEnquiryResponseDTO.builder().topUpEnquiryData(topUpEnquiryDataDTO).build();
//
//    CardSaleConfigurationDTO  cardSaleConfigurationDTO = new CardSaleConfigurationDTO(
//            "102",
//            "EMV_ANONYMOUS",
//            "1",
//            "Gold",
//            "1",
//            "MI_FARE",
//            "1",
//            "Personalised",
//            "15",
//            "25",
//            "300",
//            "AED"
//    );
//    CardAnalysisRequestDTO requestDTO = new CardAnalysisRequestDTO(
//            "1",
//            "encryptedPan123",
//            "nonEmvTag456",
//            "ksn789",
//            "formFactor1",
//            123,
//            "deviceTypeId1",
//            "deviceMode1",
//            "equipmentId1",
//            "deviceSpecialMode1",
//            "deviceId1"
//    );
//    CustomerDetailsView customerDetails = new CustomerDetailsView(
//            1, "cust123", "John", "Doe", "جون", "دو", "EN",
//            "**********", "**********"
//    );
//    ProductView productView = new ProductView(
//            1,
//            "P001",
//            "Gold Card",
//            "GOLD",
//            "ST001",
//            "ServiceType1",
//            "TIER1",
//            "TIER-SHORT",
//            "1-2-3",
//            "ACTIVE",
//            "**********"
//    );
//    ProductDto productDTO = new ProductDto("P001", "Gold Card", "GOLD", "ST001", "ServiceType1",
//            "TIER1", "TIER-SHORT", List.of(1, 2));
//
//    CardDetailsDTO cardDetailsDTO = new CardDetailsDTO("Active", "CT001", "ST001",
//            "ServiceType1", "100.00", "0.00", List.of(productDTO), List.of());
//
//    List<ProductDto> activeProducts = new ArrayList<>();
//    List<ProductDto> expiredProducts = new ArrayList<>();
//    CardDetailViewDTO cardDetail = new CardDetailViewDTO(
//            "Active",
//            "CT001",
//            "ST001",
//            "ServiceType1",
//            "100.00",
//            "0.00",
//            "5.00",
//            "Enabled"
//    );
//
//
//    EnquiryCardDetailsDTO enquiryCardDetailsDTO = new EnquiryCardDetailsDTO(
//            cardDetail,
//            activeProducts,
//            expiredProducts
//    );
//
//
//    @Test
//    public void testGetCardSaleConfiguration() {
//        List<CardProductMaster> fareMediaList = new ArrayList<>();
//        fareMediaList.add(new CardProductMaster());  // Add a mock CardProductMaster object
//        when(fareMediaRepository.findAll()).thenReturn(fareMediaList);
//        when(fareMediaMapper.toDTO(any(CardProductMaster.class))).thenReturn(new CardSaleConfigurationDTO());
//        List<CardSaleConfigurationDTO> cardSaleConfigurationDTOList = new ArrayList<>();
//        cardSaleConfigurationDTOList.add(new CardSaleConfigurationDTO());
//        CardSalesDetailsDTO actualResponse = saleDeviceTransactionsService.getCardSaleConfiguration();
//
//        verify(fareMediaRepository, times(1)).findAll();
//
//        // Verify that the toEntity() method was called once on the fareMediaMapper
//        verify(fareMediaMapper, times(1)).toDTO(any(CardProductMaster.class));
//
//    }
//
//    @Test
//    public void testSaveCardSaleConfigurations() {
//        CardProductMaster fareMedia = new CardProductMaster();
//        when(fareMediaMapper.toEntity(any(CardSaleConfigurationDTO.class))).thenReturn(fareMedia);
//        when(fareMediaRepository.save(any(CardProductMaster.class))).thenReturn(fareMedia);
//        saleDeviceTransactionsService.addCardSaleConfigurations(cardSaleConfigurationDTO);
//        verify(fareMediaRepository, times(1)).save(fareMedia);
//        verify(fareMediaMapper, times(1)).toEntity(any(CardSaleConfigurationDTO.class));
//    }
//
////    @Test
////    public void getMoneyLoadDetails_success(){
////        when(cardAccountDetailsRepository.findById(any())).thenReturn(Optional.of(new CardAccountDetails()));
////    }
//@Test
//void testAnalyzeCard_ValidRequest_ReturnsResponseDTO() {
//
//    String decryptedAccountNumber = "**********";
//
//    when(decryptionService.decryptAccountNumber(any(), any(), any()))
//            .thenReturn(decryptedAccountNumber);
//
//    when(customerDetailsRepository.findByCardAccountNumber(decryptedAccountNumber))
//            .thenReturn(customerDetails);
//
//    when(productRepository.findByCardAccountNumber(decryptedAccountNumber))
//            .thenReturn(List.of(productView));
//
//    when(productMapper.toDTO(any())).thenReturn(productDTO);
//    when(productMapper.toDTO1(any())).thenReturn(cardDetailsDTO);
//
//    CardAnalysisResponseDTO response = saleDeviceTransactionsService.analyzeCard(requestDTO);
//
//    assertNotNull(response);
//    assertEquals(200, response.getResponseCode());
//    assertEquals("success", response.getResponseMessage());
//    assertEquals(1, response.getCardDetails().size());
//}
//    @Test
//    void testAnalyzeCard_CustomerNotFound_ReturnsEmptyResponse() {
//        when(decryptionService.decryptAccountNumber(any(), any(), any()))
//                .thenReturn("**********");
//
//        when(customerDetailsRepository.findByCardAccountNumber("**********"))
//                .thenReturn(customerDetails);
//
//        CardAnalysisResponseDTO response = saleDeviceTransactionsService.analyzeCard(requestDTO);
//        assertNotNull(response);
//        assertNull(response.getCustomerDetails());
//        assertNull(response.getCardDetails().isEmpty()?null:response.getCardDetails());
//    }
//
//    @Test
//    void testEnquireCardDetails_Success() {
//        String decryptedAccountNumber = "**********";
//        when(decryptionService.decryptAccountNumber(any(), any(), any()))
//                .thenReturn(decryptedAccountNumber);
//
//        when(productRepository.findByCardAccountNumber(decryptedAccountNumber))
//                .thenReturn(List.of(productView));
//
//        when(productMapper.toDTO(any())).thenReturn(productDTO);
//        when(productMapper.toDTOFromEntity(productView)).thenReturn(enquiryCardDetailsDTO);
//
//        CardEnquiryResponseDTO response = saleDeviceTransactionsService.enquireCardDetails(requestDTO);
//
//
//
//        assertNotNull(response);
//        assertEquals(200, response.getResponseCode());
//        assertEquals("success", response.getResponseMessage());
//        assertNotNull(response.getCardDetails());
//        assertEquals(1, response.getCardDetails().size());
//
//
//        assertEquals("Active",response.getCardDetails().get(0).getCardStatus());
//        assertEquals("CT001", response.getCardDetails().get(0).getCardTypeId());
//        assertEquals("100.00", response.getCardDetails().get(0).getBalance());
//
//
//    }
//    @Test
//    void testEnquireCardDetails_NoProductsFound() {
//
//        String decryptedAccountNumber = "**********";
//        when(decryptionService.decryptAccountNumber(any(), any(), any()))
//                .thenReturn(decryptedAccountNumber);
//
//
//        when(productRepository.findByCardAccountNumber(decryptedAccountNumber))
//                .thenReturn(Collections.emptyList());
//
//        CardEnquiryResponseDTO response = saleDeviceTransactionsService.enquireCardDetails(requestDTO);
//        assertNotNull(response);
//        assertEquals(200, response.getResponseCode());
//        assertEquals("success", response.getResponseMessage());
//        assertTrue(response.getCardDetails().isEmpty());
//    }
//
//}
