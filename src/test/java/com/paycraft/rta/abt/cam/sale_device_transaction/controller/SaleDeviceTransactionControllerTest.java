//package com.paycraft.rta.abt.cam.sale_device_transaction.controller;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.paycraft.rta.abt.cam.sale_device_transaction.controller.assembler.AppResponseDtoAssembler;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardDetailViewDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.DeprecatedAppResponseDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardAnalysisRequestDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardAnalysisResponseDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardEnquiryResponseDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardSaleConfigurationDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.CardSalesDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.customer.CustomerDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.cards.EnquiryCardDetailsDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.FareMediaDTO;
//import com.paycraft.rta.abt.cam.sale_device_transaction.domain.dtos.products.ProductDto;
//import com.paycraft.rta.abt.cam.sale_device_transaction.service.CardIssuanceService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.bean.override.mockito.MockitoBean;
//import org.springframework.test.web.servlet.MockMvc;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static com.paycraft.rta.abt.cam.sale_device_transaction.domain.constants.EndpointConstants.CARD_ANALYSIS;
//import static com.paycraft.rta.abt.cam.sale_device_transaction.domain.constants.EndpointConstants.CARD_SALE_CONFIGURATION;
//import static com.paycraft.rta.abt.cam.sale_device_transaction.domain.constants.EndpointConstants.GET_CARD_DETAILS;
//import static com.paycraft.rta.abt.cam.sale_device_transaction.domain.constants.EndpointConstants.ADD_CARD_SALE_CONFIGURATIONS;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.verifyNoMoreInteractions;
//import static org.mockito.Mockito.when;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
//@ExtendWith(MockitoExtension.class)
//@WebMvcTest(CardIssuanceController.class)
//public class SaleDeviceTransactionControllerTest {
//
//    private final ObjectMapper objectMapper = new ObjectMapper();
//    @MockitoBean
//    CardIssuanceService service;
//    @Autowired
//    private MockMvc mockMvc;
//
//    @MockitoBean
//    private AppResponseDtoAssembler assembler;
//
//    FareMediaDTO fareMediaDTO;
//
//    CardSaleConfigurationDTO cardSaleConfigurationDTO;
//    CardAnalysisRequestDTO cardAnalysisRequestDTO;
//    EnquiryCardDetailsDTO enquiryCardDetailsDTO;
//
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//
//        cardSaleConfigurationDTO = new CardSaleConfigurationDTO(
//                "102",
//                "EMV_ANONYMOUS",
//                "1",
//                "Gold",
//                "1",
//                "MI_FARE",
//                "1",
//                "Personalised",
//                "15",
//                "25",
//                "300",
//                "AED"
//        );
//         cardAnalysisRequestDTO = new CardAnalysisRequestDTO(
//                "123",
//                "encryptedPan",
//                "nonEmvTagId",
//                "ksn",
//                "FormFactor",
//                100,
//                "DeviceType",
//                "DeviceMode",
//                "EquipmentId",
//                "SpecialMode",
//                "DeviceId"
//        );
//
//
//        List<ProductDto> activeProducts = new ArrayList<>();
//        List<ProductDto> expiredProducts = new ArrayList<>();
//        CardDetailViewDTO cardDetail = new CardDetailViewDTO(
//                "Active",
//                "CT001",
//                "ST001",
//                "ServiceType1",
//                "100.00",
//                "0.00",
//                "5.00",
//                "Enabled"
//        );
//        EnquiryCardDetailsDTO enquiryCardDetailsDTO = new EnquiryCardDetailsDTO(
//                cardDetail,
//                activeProducts,
//                expiredProducts
//        );
//
//
//    }
//
//    @Test
//    void enquireCardSaleConfiguration_success() throws Exception {
//        // Arrange
//        CardSalesDetailsDTO cardSaleDetailsDTO = new CardSalesDetailsDTO();
//        List<CardSaleConfigurationDTO> cardSaleConfigurationDTOList = new ArrayList<>();
//        cardSaleConfigurationDTOList.add(cardSaleConfigurationDTO);
//        cardSaleDetailsDTO.setCardSalesDetails(cardSaleConfigurationDTOList);
//        cardSaleDetailsDTO.setResponseCode(HttpStatus.OK.value());
//        cardSaleDetailsDTO.setResponseMessage("success");
//
//        when(service.getCardSaleConfiguration()).thenReturn(cardSaleDetailsDTO);
//        mockMvc.perform(get(CARD_SALE_CONFIGURATION))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.responseCode").value(200))
//                .andExpect(jsonPath("$.responseMessage").value("success"));
//        verify(service, times(1)).getCardSaleConfiguration();
//        verifyNoMoreInteractions(service);
//    }
//
//
//    @Test
//    void saveCardSaleConfigurations_success() throws Exception {
//        // Prepare
//        DeprecatedAppResponseDTO expectedResponse = DeprecatedAppResponseDTO.of(
//                null,
//                HttpStatus.OK.value(),
//                "success",
//                null,
//                null
//        );
//
//        doNothing().when(service).addCardSaleConfigurations(any(CardSaleConfigurationDTO.class));
//
//        // Perform
//        mockMvc.perform(post(ADD_CARD_SALE_CONFIGURATIONS)
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(new ObjectMapper().writeValueAsString(cardSaleConfigurationDTO)))
//                .andExpect(status().isOk()) // Expect status 200
//                .andExpect(jsonPath("$.responseCode").value(200))
//                .andExpect(jsonPath("$.responseMessage").value("success"));
//
//        // Verify
//        verify(service, times(1)).addCardSaleConfigurations(any());
//        verifyNoMoreInteractions(service);
//    }
//
//    @Test
//    public void testAnalyzeCardStatus() throws Exception {
//        CustomerDetailsDTO customerDetailsDTO = new CustomerDetailsDTO("John", "Doe", "جون", "دو", "EN");
//        CardDetailsDTO cardDetailsDTO = new CardDetailsDTO("ACTIVE", "Visa", "1234", "Credit", "1000", "200", new ArrayList<>(), new ArrayList<>());
//        CardAnalysisResponseDTO responseDTO = new CardAnalysisResponseDTO();
//        responseDTO.setCustomerDetails(customerDetailsDTO);
//
//
//        when(service.analyzeCard(cardAnalysisRequestDTO)).thenReturn(responseDTO);
//
//        ObjectMapper objectMapper = new ObjectMapper();
//        String requestJson = objectMapper.writeValueAsString(cardAnalysisRequestDTO);
//
//        mockMvc.perform(post(CARD_ANALYSIS)
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(requestJson))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.customerDetails.firstNameEng").value("John"))
//                .andExpect(jsonPath("$.customerDetails.lastNameEng").value("Doe"))
//                .andExpect(jsonPath("$.cardDetails.length()").value(1))
//                .andExpect(jsonPath("$.cardDetails[0].cardStatus").value("ACTIVE"))
//                .andExpect(jsonPath("$.cardDetails[0].cardTypeId").value("Visa"))
//                .andExpect(jsonPath("$.cardDetails[0].balance").value("1000"));
//    }
//
//    @Test
//    public void testEnquireCardDetails() throws Exception {
//
//        CardEnquiryResponseDTO responseDTO = new CardEnquiryResponseDTO();
//        responseDTO.setCardDetails(List.of(enquiryCardDetailsDTO));
//
//        when(service.enquireCardDetails(cardAnalysisRequestDTO)).thenReturn(responseDTO);
//
//
//        ObjectMapper objectMapper = new ObjectMapper();
//        String requestJson = objectMapper.writeValueAsString(cardAnalysisRequestDTO);
//
//
//        mockMvc.perform(post(GET_CARD_DETAILS)
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(requestJson))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.cardDetails.length()").value(1))
//                .andExpect(jsonPath("$.cardDetails[0].cardStatus").value("ACTIVE"))
//                .andExpect(jsonPath("$.cardDetails[0].balance").value("1000"))
//                .andExpect(jsonPath("$.cardDetails[0].holdBalance").value("150"))
//                .andExpect(jsonPath("$.cardDetails[0].autoReloadStatus").value("Enabled"));
//    }
//
//}
